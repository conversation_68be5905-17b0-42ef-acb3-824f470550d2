/**
 * Video Player Looping Manager
 * 
 * This class manages the looping state and integrates with the rule engine
 * to provide clean, predictable looping behavior.
 */

import { loopingRuleEngine, LoopingState, TimeframeData, VideoLoopSettings, PlaybackSegment } from './LoopingRuleEngine'

export interface VideoPlayerState {
  currentTimeframeIndex: number
  isInTimeframeMode: boolean
  currentTime: number
}

export class VideoPlayerLoopingManager {
  private loopingStates: Map<string, LoopingState> = new Map()
  private onStateUpdate?: (videoId: string, state: VideoPlayerState) => void
  private onSeekTo?: (time: number) => void
  private onVideoComplete?: () => void

  constructor(
    onStateUpdate?: (videoId: string, state: VideoPlayerState) => void,
    onSeekTo?: (time: number) => void,
    onVideoComplete?: () => void
  ) {
    this.onStateUpdate = onStateUpdate
    this.onSeekTo = onSeekTo
    this.onVideoComplete = onVideoComplete
  }

  /**
   * Initialize or reset state for a video
   */
  initializeVideo(
    videoId: string,
    timeframes: TimeframeData[],
    loopSettings: VideoLoopSettings,
    queueLoopCount: number
  ): void {
    console.log(`🎬 [LOOP MANAGER] Initializing video ${videoId} with loop settings:`, loopSettings)
    console.log(`🔍 [DEBUG] Video ${videoId} - videoLoopCount from settings: ${loopSettings.videoLoopCount}`)

    // Check if video already has state
    const existingState = this.loopingStates.get(videoId)
    if (existingState) {
      console.log(`🎬 [LOOP MANAGER] Video ${videoId} already has state - reinitializing:`, existingState)
    }

    const state = loopingRuleEngine.createInitialState(videoId, timeframes, queueLoopCount)
    console.log(`🔍 [DEBUG] Created initial state for ${videoId}:`, state)

    // For timeframes-only mode, initialize timeframe counters immediately
    if (loopSettings.loopMode === 'timeframes-only' && timeframes.length > 0) {
      timeframes.forEach(tf => {
        state.timeframeLoopCounts[tf.id] = 0
      })
    }

    this.loopingStates.set(videoId, state)

    // Determine if we should start in timeframe mode
    const hasStartedTimeframes = loopSettings.loopMode === 'timeframes-only'
    
    this.updateUIState(videoId, {
      currentTimeframeIndex: 0,
      isInTimeframeMode: hasStartedTimeframes,
      currentTime: 0
    })

    console.log(`🎬 [LOOP MANAGER] Video ${videoId} initialized:`, {
      mode: loopSettings.loopMode,
      timeframes: timeframes.length,
      startInTimeframeMode: hasStartedTimeframes
    })
  }

  /**
   * Get the start position for a video
   */
  getVideoStartPosition(videoId: string, timeframes: TimeframeData[], loopSettings: VideoLoopSettings): number {
    const startPos = loopingRuleEngine.getVideoStartPosition(timeframes, loopSettings)
    console.log(`🎬 [LOOP MANAGER] Video ${videoId} start position: ${startPos}s`)
    return startPos
  }

  /**
   * Check if timeframe monitoring should start immediately
   */
  shouldStartTimeframeMonitoring(
    videoId: string,
    timeframes: TimeframeData[],
    loopSettings: VideoLoopSettings
  ): boolean {
    const state = this.loopingStates.get(videoId)
    if (!state) return false

    const hasStartedTimeframes = Object.keys(state.timeframeLoopCounts).length > 0
    const shouldStart = loopingRuleEngine.shouldStartTimeframeMonitoring(timeframes, loopSettings, hasStartedTimeframes)
    
    console.log(`🎬 [LOOP MANAGER] Should start monitoring for ${videoId}:`, shouldStart)
    return shouldStart
  }

  /**
   * Handle when a timeframe ends
   */
  handleTimeframeEnd(
    videoId: string,
    timeframes: TimeframeData[],
    loopSettings: VideoLoopSettings
  ): void {
    const state = this.loopingStates.get(videoId)
    if (!state) {
      console.error(`❌ [LOOP MANAGER] No state found for video ${videoId}`)
      return
    }

    console.log(`🎬 [LOOP MANAGER] Timeframe ended for ${videoId}:`, {
      currentIndex: state.currentTimeframeIndex,
      videoLoops: state.videoLoopCount,
      mode: loopSettings.loopMode
    })

    // Get next action from rule engine
    const segment = loopingRuleEngine.getNextPlaybackSegment(timeframes, loopSettings, state)
    console.log(`🎯 [LOOP MANAGER] Next segment:`, segment)

    // Execute the segment
    this.executePlaybackSegment(videoId, segment, timeframes, loopSettings)
  }

  /**
   * Handle when a video ends (no timeframes or after timeframes complete)
   */
  handleVideoEnd(
    videoId: string,
    timeframes: TimeframeData[],
    loopSettings: VideoLoopSettings
  ): 'continue' | 'complete' {
    const state = this.loopingStates.get(videoId)
    if (!state) {
      console.error(`❌ [LOOP MANAGER] No state found for video ${videoId}`)
      return 'complete'
    }

    console.log(`🎬 [LOOP MANAGER] Video ended for ${videoId}`)

    // For whole-video mode, check if we need to start timeframes
    if (loopSettings.loopMode === 'whole-video-plus-timeframes' && timeframes.length > 0) {
      const hasStartedTimeframes = Object.keys(state.timeframeLoopCounts).length > 0
      
      if (!hasStartedTimeframes) {
        console.log(`🎬 [LOOP MANAGER] Starting timeframes after whole video`)
        this.startTimeframes(videoId, timeframes)
        return 'continue'
      }
    }

    // Get next action from rule engine
    const segment = loopingRuleEngine.getNextPlaybackSegment(timeframes, loopSettings, state)
    console.log(`🎯 [LOOP MANAGER] Next segment after video end:`, segment)

    // Always execute the segment to ensure callbacks are triggered
    this.executePlaybackSegment(videoId, segment, timeframes, loopSettings)

    if (segment.type === 'video-complete') {
      return 'complete'
    }

    return 'continue'
  }

  /**
   * Execute a playback segment
   */
  private executePlaybackSegment(
    videoId: string,
    segment: PlaybackSegment,
    timeframes: TimeframeData[],
    loopSettings: VideoLoopSettings
  ): void {
    const state = this.loopingStates.get(videoId)
    if (!state) return

    console.log(`🎬 [LOOP MANAGER] Executing segment: ${segment.type} - ${segment.message}`)

    // Update internal state
    const currentTimeframe = timeframes[state.currentTimeframeIndex]
    const updatedState = loopingRuleEngine.updateStateAfterSegment(
      state,
      segment,
      currentTimeframe?.id
    )
    this.loopingStates.set(videoId, updatedState)

    // Execute the action
    switch (segment.type) {
      case 'timeframe-loop':
      case 'next-timeframe':
        if (segment.seekTo !== undefined) {
          this.onSeekTo?.(segment.seekTo)
        }

        // Update UI state - always in timeframe mode for these actions
        this.updateUIState(videoId, {
          currentTimeframeIndex: segment.timeframeIndex ?? updatedState.currentTimeframeIndex,
          isInTimeframeMode: true,
          currentTime: segment.seekTo ?? 0
        })
        break

      case 'video-loop':
        if (segment.seekTo !== undefined) {
          this.onSeekTo?.(segment.seekTo)
        }

        // Update UI state - timeframe mode depends on whether video has timeframes
        this.updateUIState(videoId, {
          currentTimeframeIndex: segment.timeframeIndex ?? updatedState.currentTimeframeIndex,
          isInTimeframeMode: timeframes.length > 0,
          currentTime: segment.seekTo ?? 0
        })
        break

      case 'start-timeframes':
        if (segment.seekTo !== undefined) {
          this.onSeekTo?.(segment.seekTo)
        }
        
        // Initialize timeframe counters
        timeframes.forEach(tf => {
          updatedState.timeframeLoopCounts[tf.id] = 0
        })
        this.loopingStates.set(videoId, updatedState)
        
        this.updateUIState(videoId, {
          currentTimeframeIndex: 0,
          isInTimeframeMode: true,
          currentTime: segment.seekTo ?? 0
        })
        break

      case 'continue-video':
        if (segment.seekTo !== undefined) {
          this.onSeekTo?.(segment.seekTo)
        }

        // Update UI state - exit timeframe mode, continue normal video playback
        this.updateUIState(videoId, {
          currentTimeframeIndex: updatedState.currentTimeframeIndex,
          isInTimeframeMode: false,
          currentTime: segment.seekTo ?? 0
        })
        break

      case 'video-complete':
        this.onVideoComplete?.()
        break
    }
  }

  /**
   * Start timeframes for whole-video mode
   */
  private startTimeframes(videoId: string, timeframes: TimeframeData[]): void {
    if (timeframes.length === 0) return

    const firstTimeframe = timeframes[0]
    this.onSeekTo?.(firstTimeframe.startTime)

    // Initialize timeframe counters
    const state = this.loopingStates.get(videoId)
    if (state) {
      timeframes.forEach(tf => {
        state.timeframeLoopCounts[tf.id] = 0
      })
      this.loopingStates.set(videoId, state)
    }

    this.updateUIState(videoId, {
      currentTimeframeIndex: 0,
      isInTimeframeMode: true,
      currentTime: firstTimeframe.startTime
    })
  }

  /**
   * Update UI state
   */
  private updateUIState(videoId: string, uiState: VideoPlayerState): void {
    this.onStateUpdate?.(videoId, uiState)
  }

  /**
   * Update current time (called during monitoring)
   */
  updateCurrentTime(videoId: string, currentTime: number): void {
    const state = this.loopingStates.get(videoId)
    if (!state) return

    this.updateUIState(videoId, {
      currentTimeframeIndex: state.currentTimeframeIndex,
      isInTimeframeMode: Object.keys(state.timeframeLoopCounts).length > 0,
      currentTime
    })
  }

  /**
   * Get current state for a video
   */
  getState(videoId: string): LoopingState | undefined {
    return this.loopingStates.get(videoId)
  }

  /**
   * Clear all states (when queue changes)
   */
  clearAllStates(): void {
    console.log(`🎬 [LOOP MANAGER] Clearing all states`)
    this.loopingStates.clear()
  }

  /**
   * Clear all states except for a specific video (safer for queue changes)
   */
  clearAllStatesExcept(keepVideoId?: string): void {
    if (!keepVideoId) {
      this.clearAllStates()
      return
    }

    console.log(`🎬 [LOOP MANAGER] Clearing all states except ${keepVideoId}`)
    const keepState = this.loopingStates.get(keepVideoId)
    this.loopingStates.clear()
    if (keepState) {
      this.loopingStates.set(keepVideoId, keepState)
      console.log(`🎬 [LOOP MANAGER] Preserved state for ${keepVideoId}`)
    }
  }
}
