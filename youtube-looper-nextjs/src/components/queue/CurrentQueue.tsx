'use client'

import { useQueue } from '@/hooks/useQueue'
import { QueueItem } from './QueueItem'
import { LoadQueueInput } from './LoadQueueInput'
import { ShareQueueButton } from './ShareQueueButton'

export function CurrentQueue() {
  const { items, currentIndex, queueLoopCount, clearQueue, hasNext, restartQueue } = useQueue()

  if (items.length === 0) {
    return (
      <div className="glassmorphism rounded-2xl p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-white">Current Queue</h2>
          <LoadQueueInput />
        </div>
        
        <div className="text-center py-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-dark-700 rounded-full flex items-center justify-center">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="text-dark-400">
              <path d="M15 6H3v2h12V6zm0 4H3v2h12v-2zM3 16h8v-2H3v2zM17 6v8.18c-.31-.11-.65-.18-1-.18-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3V8h3V6h-5z"/>
            </svg>
          </div>
          <p className="text-dark-300 mb-2">Your queue is empty</p>
          <p className="text-sm text-dark-400">Add videos to start playing!</p>
        </div>
      </div>
    )
  }

  return (
    <div className="glassmorphism rounded-2xl overflow-hidden">
      <div className="p-4 border-b border-white/10">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold text-white">
              Current Queue ({items.length})
            </h2>
            <div className="flex items-center gap-2 mt-1">
              {queueLoopCount === 0 && !hasNext ? (
                // Queue completed - show repeat button
                <button
                  onClick={() => restartQueue(-1)}
                  className="flex items-center gap-1 text-xs px-2 py-1 rounded bg-primary-600/20 text-primary-300 hover:bg-primary-600/30 transition-colors"
                  title="Restart queue"
                >
                  <svg width="12" height="12" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"/>
                  </svg>
                  Repeat
                </button>
              ) : (
                // Normal queue loop display
                <>
                  <span className="text-xs text-dark-400">Queue loops:</span>
                  <span className={`text-xs px-2 py-0.5 rounded ${
                    queueLoopCount === -1
                      ? 'bg-green-600/20 text-green-300'
                      : 'bg-blue-600/20 text-blue-300'
                  }`}>
                    {queueLoopCount === -1 ? '∞ Infinite' : `${queueLoopCount}x`}
                  </span>
                </>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <LoadQueueInput />
            <ShareQueueButton />
            <button
              onClick={clearQueue}
              className="btn-secondary text-sm px-3 py-1"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor" className="mr-1">
                <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
              </svg>
              Clear
            </button>
          </div>
        </div>
      </div>
      
      <div className="max-h-96 overflow-y-auto">
        {items.map((item, index) => (
          <QueueItem
            key={`queue-item-${item.id}-${item.addedAt}-${item.queueIndex}-${index}`}
            item={item}
            index={index}
            isActive={index === currentIndex}
            showRemove={true}
          />
        ))}
      </div>
    </div>
  )
}
